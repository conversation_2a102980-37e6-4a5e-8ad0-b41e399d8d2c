import bpy
import re
import os
import sys
import json
from typing import List, Dict, Any, Optional
import time

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

def get_api_key(context, addon_name):
    """Get API key from addon preferences"""
    try:
        preferences = context.preferences
        addon_prefs = preferences.addons[addon_name].preferences
        return addon_prefs.api_key
    except (KeyError, AttributeError):
        return ""

def init_props():
    """Initialize all custom properties"""
    # Chat history property
    bpy.types.Scene.gpt4_chat_history = bpy.props.CollectionProperty(
        type=bpy.types.PropertyGroup,
        name="Chat History",
        description="Chat history with GPT-4"
    )
    
    # Model selection with updated models for 2025
    bpy.types.Scene.gpt4_model = bpy.props.EnumProperty(
        name="GPT Model",
        description="Select the GPT model to use",
        items=[
            ("gpt-4", "GPT-4", "GPT-4 model (most capable)"),
            ("gpt-4-turbo", "GPT-4 Turbo", "GPT-4 Turbo (faster, cheaper)"),
            ("gpt-4o", "GPT-4o", "GPT-4o (multimodal, faster)"),
            ("gpt-4o-mini", "GPT-4o Mini", "GPT-4o Mini (fastest, cheapest)"),
            ("gpt-3.5-turbo", "GPT-3.5 Turbo", "GPT-3.5 Turbo (legacy)"),
        ],
        default="gpt-4o",
    )
    
    # Chat input
    bpy.types.Scene.gpt4_chat_input = bpy.props.StringProperty(
        name="Message",
        description="Enter your message to GPT-4",
        default="",
        maxlen=1000,
    )
    
    # Button state
    bpy.types.Scene.gpt4_button_pressed = bpy.props.BoolProperty(
        name="Button Pressed",
        description="Internal state for button",
        default=False
    )
    
    # Add properties to PropertyGroup for message storage
    bpy.types.PropertyGroup.type = bpy.props.StringProperty(
        name="Message Type",
        description="Type of message (user or assistant)",
        default="user"
    )
    
    bpy.types.PropertyGroup.content = bpy.props.StringProperty(
        name="Message Content",
        description="Content of the message",
        default=""
    )
    
    bpy.types.PropertyGroup.timestamp = bpy.props.StringProperty(
        name="Timestamp",
        description="When the message was created",
        default=""
    )

def clear_props():
    """Clear all custom properties"""
    props_to_clear = [
        'gpt4_chat_history',
        'gpt4_chat_input', 
        'gpt4_button_pressed',
        'gpt4_model'
    ]
    
    for prop in props_to_clear:
        try:
            delattr(bpy.types.Scene, prop)
        except AttributeError:
            pass

def clean_code_response(response_text: str) -> str:
    """Clean and extract Python code from GPT response"""
    if not response_text:
        return ""
    
    # Remove markdown code blocks
    code_blocks = re.findall(r'```(?:python)?\s*(.*?)```', response_text, re.DOTALL)
    
    if code_blocks:
        # Use the first code block found
        code = code_blocks[0]
    else:
        # If no code blocks, assume the entire response is code
        code = response_text
    
    # Remove leading/trailing whitespace
    code = code.strip()
    
    # Remove common markdown artifacts
    code = re.sub(r'^python\s*\n', '', code, flags=re.MULTILINE)
    code = re.sub(r'^```\s*\n', '', code, flags=re.MULTILINE)
    code = re.sub(r'\n```\s*$', '', code, flags=re.MULTILINE)
    
    return code

def validate_blender_code(code: str) -> tuple[bool, str]:
    """Validate that the generated code is safe to execute"""
    if not code.strip():
        return False, "Empty code"
    
    # Check for potentially dangerous operations
    dangerous_patterns = [
        r'import\s+os\s*\.',
        r'import\s+sys\s*\.',
        r'import\s+subprocess',
        r'exec\s*\(',
        r'eval\s*\(',
        r'open\s*\(',
        r'file\s*\(',
        r'__import__',
        r'reload\s*\(',
        r'compile\s*\(',
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, f"Potentially dangerous code detected: {pattern}"
    
    # Check for required bpy import
    if 'bpy' not in code and 'import bpy' not in code:
        return False, "Code should import and use bpy module"
    
    return True, "Code validation passed"

def generate_blender_code(
    prompt: str, 
    chat_history: List[Dict[str, str]], 
    context, 
    system_prompt: str,
    api_key: str
) -> Optional[str]:
    """Generate Blender code using OpenAI API"""
    
    if not OPENAI_AVAILABLE:
        return "Error: OpenAI package not available"
    
    if not api_key:
        return "Error: No API key provided"
    
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=api_key)
        
        # Prepare conversation history
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add recent chat history (last 10 messages to avoid token limit)
        for message in list(chat_history)[-10:]:
            if hasattr(message, 'type') and hasattr(message, 'content'):
                if message.type == "assistant":
                    messages.append({
                        "role": "assistant", 
                        "content": f"```python\n{message.content}\n```"
                    })
                else:
                    messages.append({
                        "role": "user", 
                        "content": message.content
                    })
        
        # Add current user message
        user_message = (
            f"Can you please write Blender Python code for me that accomplishes the following task: {prompt}?\n"
            f"Important requirements:\n"
            f"- Use only Python code in your response\n"
            f"- Use modern Blender API (3.0+)\n"
            f"- Include error handling where appropriate\n"
            f"- Do not provide explanations, only code\n"
            f"- Ensure the code is safe to execute"
        )
        
        messages.append({"role": "user", "content": user_message})
        
        # Get preferences for advanced settings
        try:
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__.split('.')[0]].preferences
            max_tokens = addon_prefs.max_tokens
            temperature = addon_prefs.temperature
        except (KeyError, AttributeError):
            max_tokens = 1500
            temperature = 0.3
        
        # Make API call
        response = client.chat.completions.create(
            model=context.scene.gpt4_model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=False,  # Disable streaming for simplicity
        )
        
        # Extract response
        if response.choices and len(response.choices) > 0:
            raw_response = response.choices[0].message.content
            
            # Clean and validate the code
            cleaned_code = clean_code_response(raw_response)
            
            if not cleaned_code:
                return "Error: No valid code generated"
            
            # Validate the code
            is_valid, validation_message = validate_blender_code(cleaned_code)
            
            if not is_valid:
                return f"Error: {validation_message}"
            
            return cleaned_code
        
        else:
            return "Error: No response from OpenAI"
            
    except Exception as e:
        error_msg = f"Error generating code: {str(e)}"
        print(f"OpenAI API Error: {e}")
        return error_msg

def split_area_to_text_editor(context):
    """Split current area to create a text editor"""
    try:
        area = context.area
        if not area:
            return None
            
        # Find a suitable region to split
        for region in area.regions:
            if region.type == 'WINDOW':
                # Split the area
                override = {'area': area, 'region': region}
                bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
                break
        
        # Get the new area (should be the last one)
        if context.screen.areas:
            new_area = context.screen.areas[-1]
            new_area.type = 'TEXT_EDITOR'
            return new_area
            
    except Exception as e:
        print(f"Error splitting area: {e}")
        return None

def get_model_info(model_name: str) -> Dict[str, Any]:
    """Get information about a specific model"""
    model_info = {
        "gpt-4": {
            "name": "GPT-4",
            "description": "Most capable model",
            "max_tokens": 8192,
            "cost_per_1k_tokens": 0.03,
        },
        "gpt-4-turbo": {
            "name": "GPT-4 Turbo",
            "description": "Faster and cheaper than GPT-4",
            "max_tokens": 128000,
            "cost_per_1k_tokens": 0.01,
        },
        "gpt-4o": {
            "name": "GPT-