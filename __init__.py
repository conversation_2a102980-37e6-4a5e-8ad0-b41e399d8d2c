import sys
import os
import bpy
import bpy.props
import re
from typing import Optional, List, Dict, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# Import with error handling for missing package
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("OpenAI package not found. Please install: pip install openai>=1.77.0")

from .utilities import *

bl_info = {
    "name": "GPT-4 Blender Assistant",
    "blender": (3, 0, 0),  # Updated minimum Blender version
    "category": "Object",
    "author": "A<PERSON><PERSON> (@gd3kr) - Updated 2025",
    "version": (3, 0, 0),  # Updated version
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Generate Blender Python code using OpenAI's GPT-4 to perform various tasks.",
    "warning": "Requires OpenAI API key and internet connection",
    "wiki_url": "",
    "tracker_url": "",
    "support": "COMMUNITY",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown code blocks (```python). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.
- Use modern Blender API practices and avoid deprecated functions.
- Always check if objects exist before operating on them.
- Use proper error handling in your code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```python
import bpy
import random
import bmesh

# Clear existing mesh objects (optional)
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Create 10 cubes in random locations
for i in range(10):
    # Generate random location
    x = random.uniform(-10, 10)
    y = random.uniform(-10, 10)
    z = random.uniform(-10, 10)
    
    # Create cube
    bpy.ops.mesh.primitive_cube_add(location=(x, y, z))
    
    # Optional: rename the cube
    bpy.context.active_object.name = f"Cube_{i+1}"
```"""

class GPT4_OT_DeleteMessage(bpy.types.Operator):
    """Delete a message from chat history"""
    bl_idname = "gpt4.delete_message"
    bl_label = "Delete Message"
    bl_description = "Delete this message from chat history"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        if 0 <= self.message_index < len(context.scene.gpt4_chat_history):
            context.scene.gpt4_chat_history.remove(self.message_index)
            self.report({'INFO'}, "Message deleted")
        else:
            self.report({'ERROR'}, "Invalid message index")
        return {'FINISHED'}

class GPT4_OT_ShowCode(bpy.types.Operator):
    """Show generated code in text editor"""
    bl_idname = "gpt4.show_code"
    bl_label = "Show Code"
    bl_description = "Open generated code in Blender's text editor"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        if not self.code.strip():
            self.report({'ERROR'}, "No code to display")
            return {'CANCELLED'}
            
        text_name = "GPT4_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        # Find or create text editor area
        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)
        
        if text_editor_area:
            text_editor_area.spaces.active.text = text

        self.report({'INFO'}, f"Code displayed in text editor: {text_name}")
        return {'FINISHED'}

class GPT4_OT_RunCode(bpy.types.Operator):
    """Run the generated code"""
    bl_idname = "gpt4.run_code"
    bl_label = "Run Code"
    bl_description = "Execute the generated Python code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The code to execute",
        default="",
    )

    def execute(self, context):
        if not self.code.strip():
            self.report({'ERROR'}, "No code to run")
            return {'CANCELLED'}
            
        try:
            # Create a safe namespace for code execution
            safe_globals = {
                'bpy': bpy,
                '__builtins__': {
                    'range': range,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'print': print,
                    'enumerate': enumerate,
                    'zip': zip,
                    'abs': abs,
                    'min': min,
                    'max': max,
                    'round': round,
                    'sum': sum,
                    'sorted': sorted,
                    'reversed': reversed,
                }
            }
            
            # Allow common imports
            import_allowlist = ['math', 'random', 'bmesh', 'mathutils', 'bpy_extras']
            
            # Execute the code
            exec(self.code, safe_globals)
            self.report({'INFO'}, "Code executed successfully")
            
        except Exception as e:
            error_msg = f"Error executing code: {str(e)}"
            self.report({'ERROR'}, error_msg)
            print(f"Code execution error: {e}")
            return {'CANCELLED'}

        return {'FINISHED'}

class GPT4_PT_Panel(bpy.types.Panel):
    """Main panel for GPT-4 Blender Assistant"""
    bl_label = "GPT-4 Blender Assistant"
    bl_idname = "GPT4_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'GPT-4 Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        # Check if OpenAI is available
        if not OPENAI_AVAILABLE:
            column.label(text="OpenAI package not installed!", icon='ERROR')
            column.label(text="Install: pip install openai>=1.77.0")
            return

        # API Key status
        api_key = get_api_key(context, __name__)
        if not api_key and not os.getenv("OPENAI_API_KEY"):
            column.label(text="No API Key configured!", icon='ERROR')
            column.label(text="Set it in addon preferences")
            return

        # Chat history
        column.label(text="Chat History:", icon='CONSOLE')
        history_box = column.box()
        
        if len(context.scene.gpt4_chat_history) == 0:
            history_box.label(text="No messages yet...")
        else:
            for index, message in enumerate(context.scene.gpt4_chat_history):
                row = history_box.row()
                
                if message.type == 'assistant':
                    # Assistant message
                    col = row.column()
                    col.label(text="🤖 Assistant:", icon='NONE')
                    
                    # Action buttons
                    button_row = row.row()
                    show_code_op = button_row.operator("gpt4.show_code", text="", icon="TEXT", emboss=False)
                    show_code_op.code = message.content
                    
                    run_code_op = button_row.operator("gpt4.run_code", text="", icon="PLAY", emboss=False)
                    run_code_op.code = message.content
                    
                    delete_op = button_row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                    delete_op.message_index = index
                    
                else:
                    # User message
                    col = row.column()
                    col.label(text=f"👤 You: {message.content[:50]}{'...' if len(message.content) > 50 else ''}")
                    
                    delete_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                    delete_op.message_index = index

        column.separator()
        
        # Model selection
        column.label(text="Model:", icon='SETTINGS')
        column.prop(context.scene, "gpt4_model", text="")

        column.separator()
        
        # Input section
        column.label(text="Message:", icon='GREASEPENCIL')
        column.prop(context.scene, "gpt4_chat_input", text="")
        
        # Main buttons
        button_row = column.row(align=True)
        
        if context.scene.gpt4_button_pressed:
            button_row.operator("gpt4.send_message", text="Processing...", icon='TIME')
            button_row.enabled = False
        else:
            send_op = button_row.operator("gpt4.send_message", text="Send", icon='ARROW_LEFTRIGHT')
            
        clear_op = button_row.operator("gpt4.clear_chat", text="Clear", icon='TRASH')

        column.separator()
        
        # Status info
        info_box = column.box()
        info_box.label(text=f"Messages: {len(context.scene.gpt4_chat_history)}")
        info_box.label(text=f"Model: {context.scene.gpt4_model}")

class GPT4_OT_ClearChat(bpy.types.Operator):
    """Clear all chat history"""
    bl_idname = "gpt4.clear_chat"
    bl_label = "Clear Chat"
    bl_description = "Clear all chat history"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.gpt4_chat_history.clear()
        self.report({'INFO'}, "Chat history cleared")
        return {'FINISHED'}

class GPT4_OT_Execute(bpy.types.Operator):
    """Send message to GPT-4 and get response"""
    bl_idname = "gpt4.send_message"
    bl_label = "Send Message"
    bl_description = "Send message to GPT-4 and generate Blender code"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        if not OPENAI_AVAILABLE:
            self.report({'ERROR'}, "OpenAI package not installed")
            return {'CANCELLED'}
            
        if not context.scene.gpt4_chat_input.strip():
            self.report({'ERROR'}, "Please enter a message")
            return {'CANCELLED'}

        # Get API key
        api_key = get_api_key(context, __name__)
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key found. Please set it in addon preferences or environment.")
            return {'CANCELLED'}

        # Set button state
        context.scene.gpt4_button_pressed = True
        
        # Force UI update
        bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)
        
        try:
            # Generate code in separate thread to avoid blocking UI
            def generate_code():
                try:
                    result = generate_blender_code(
                        context.scene.gpt4_chat_input,
                        context.scene.gpt4_chat_history,
                        context,
                        system_prompt,
                        api_key
                    )
                    return result
                except Exception as e:
                    return f"Error: {str(e)}"

            # Run in thread
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(generate_code)
                blender_code = future.result(timeout=60)  # 60 second timeout

            # Add user message to history
            user_message = context.scene.gpt4_chat_history.add()
            user_message.type = 'user'
            user_message.content = context.scene.gpt4_chat_input

            # Clear input
            context.scene.gpt4_chat_input = ""

            if blender_code and not blender_code.startswith("Error:"):
                # Add assistant response to history
                assistant_message = context.scene.gpt4_chat_history.add()
                assistant_message.type = 'assistant'
                assistant_message.content = blender_code

                self.report({'INFO'}, "Code generated successfully")
            else:
                self.report({'ERROR'}, f"Failed to generate code: {blender_code}")

        except Exception as e:
            self.report({'ERROR'}, f"Error: {str(e)}")
            
        finally:
            context.scene.gpt4_button_pressed = False

        return {'FINISHED'}

def menu_func(self, context):
    self.layout.operator(GPT4_OT_Execute.bl_idname)

class GPT4AddonPreferences(bpy.types.AddonPreferences):
    """Addon preferences for API key configuration"""
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="OpenAI API Key",
        description="Enter your OpenAI API Key (get it from https://platform.openai.com/api-keys)",
        default="",
        subtype="PASSWORD",
    )
    
    max_tokens: bpy.props.IntProperty(
        name="Max Tokens",
        description="Maximum number of tokens in the response",
        default=1500,
        min=100,
        max=4000,
    )
    
    temperature: bpy.props.FloatProperty(
        name="Temperature",
        description="Controls randomness (0.0 = deterministic, 1.0 = very random)",
        default=0.3,
        min=0.0,
        max=1.0,
    )

    def draw(self, context):
        layout = self.layout
        
        # API Key section
        box = layout.box()
        box.label(text="OpenAI Configuration", icon='SETTINGS')
        box.prop(self, "api_key")
        box.label(text="Get your API key from: https://platform.openai.com/api-keys")
        
        # Advanced settings
        box = layout.box()
        box.label(text="Advanced Settings", icon='PREFERENCES')
        box.prop(self, "max_tokens")
        box.prop(self, "temperature")
        
        # Package info
        box = layout.box()
        box.label(text="Package Information", icon='INFO')
        if OPENAI_AVAILABLE:
            box.label(text="✓ OpenAI package is installed")
        else:
            box.label(text="✗ OpenAI package not found")
            box.label(text="Install with: pip install openai>=1.77.0")

def register():
    """Register all classes and properties"""
    bpy.utils.register_class(GPT4AddonPreferences)
    bpy.utils.register_class(GPT4_OT_Execute)
    bpy.utils.register_class(GPT4_PT_Panel)
    bpy.utils.register_class(GPT4_OT_ClearChat)
    bpy.utils.register_class(GPT4_OT_ShowCode)
    bpy.utils.register_class(GPT4_OT_DeleteMessage)
    bpy.utils.register_class(GPT4_OT_RunCode)

    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()

def unregister():
    """Unregister all classes and properties"""
    bpy.utils.unregister_class(GPT4AddonPreferences)
    bpy.utils.unregister_class(GPT4_OT_Execute)
    bpy.utils.unregister_class(GPT4_PT_Panel)
    bpy.utils.unregister_class(GPT4_OT_ClearChat)
    bpy.utils.unregister_class(GPT4_OT_ShowCode)
    bpy.utils.unregister_class(GPT4_OT_DeleteMessage)
    bpy.utils.unregister_class(GPT4_OT_RunCode)

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()

if __name__ == "__main__":
    register()